import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import apiService from '@/services/apiService';

export const ProfileUploadTest: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const { toast } = useToast();
  const { user, updateProfile } = useAuth();

  const handleTestUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log('=== PROFILE UPLOAD TEST ===');
    console.log('File:', file.name, file.size, file.type);
    console.log('User:', user);
    console.log('Token:', apiService.getToken());

    setIsUploading(true);
    setUploadResult(null);

    try {
      // Test 1: Check if user is authenticated
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Test 2: Check if token exists
      const token = apiService.getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      // Test 3: Try to upload
      console.log('Starting upload...');
      const response = await apiService.uploadProfilePicture(file);
      console.log('Upload response:', response);

      // Test 4: Try to update profile
      console.log('Updating profile...');
      await updateProfile({ profileImage: response.data.profileImageUrl });

      setUploadResult({
        success: true,
        response,
        message: 'Upload successful!'
      });

      toast({
        title: "Test Upload Successful",
        description: "Profile picture uploaded and updated successfully.",
      });

    } catch (error: any) {
      console.error('Upload test failed:', error);
      setUploadResult({
        success: false,
        error: error.message,
        stack: error.stack
      });

      toast({
        title: "Test Upload Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Profile Picture Upload Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p><strong>User:</strong> {user?.name || 'Not logged in'}</p>
          <p><strong>Email:</strong> {user?.email || 'N/A'}</p>
          <p><strong>Current Profile Image:</strong> {user?.profileImage || 'None'}</p>
          <p><strong>Token:</strong> {apiService.getToken() ? 'Present' : 'Missing'}</p>
        </div>

        <div>
          <input
            type="file"
            accept="image/jpeg,image/png,image/jpg"
            onChange={handleTestUpload}
            disabled={isUploading}
            className="mb-4"
          />
          <Button 
            onClick={() => document.querySelector('input[type="file"]')?.click()}
            disabled={isUploading}
          >
            {isUploading ? 'Uploading...' : 'Test Upload Profile Picture'}
          </Button>
        </div>

        {uploadResult && (
          <div className="mt-4 p-4 border rounded">
            <h3 className="font-bold mb-2">
              {uploadResult.success ? '✅ Success' : '❌ Failed'}
            </h3>
            <pre className="text-sm bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(uploadResult, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
