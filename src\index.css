@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* AgriLift Dark Theme Styles */
@layer components {
  /* Light theme gradients (default) */
  .agrilift-gradient {
    @apply bg-gradient-to-br from-green-50 to-blue-50;
  }

  .agrilift-gradient-primary {
    @apply bg-gradient-to-r from-green-600 to-green-700;
  }

  .agrilift-gradient-secondary {
    @apply bg-gradient-to-r from-green-100 to-green-200;
  }

  .agrilift-card {
    @apply bg-white border border-gray-200 shadow-sm;
  }

  .agrilift-card-header {
    @apply bg-gray-50 border-b border-gray-200;
  }

  .agrilift-text-primary {
    @apply text-gray-900;
  }

  .agrilift-text-secondary {
    @apply text-gray-600;
  }

  .agrilift-text-muted {
    @apply text-gray-500;
  }

  .agrilift-border {
    @apply border-gray-200;
  }

  .agrilift-input {
    @apply bg-white border-gray-300 text-gray-900 placeholder-gray-500;
  }

  .agrilift-button-primary {
    @apply bg-green-600 hover:bg-green-700 text-white;
  }

  .agrilift-button-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-900;
  }

  /* Dark theme overrides */
  .dark .agrilift-gradient {
    @apply bg-gradient-to-br from-gray-900 to-gray-800;
  }

  .dark .agrilift-gradient-primary {
    @apply bg-gradient-to-r from-green-700 to-green-800;
  }

  .dark .agrilift-gradient-secondary {
    @apply bg-gradient-to-r from-green-800 to-green-900;
  }

  .dark .agrilift-card {
    @apply bg-gray-800 border-gray-700 shadow-lg;
  }

  .dark .agrilift-card-header {
    @apply bg-gray-700 border-gray-600;
  }

  .dark .agrilift-text-primary {
    @apply text-gray-100;
  }

  .dark .agrilift-text-secondary {
    @apply text-gray-300;
  }

  .dark .agrilift-text-muted {
    @apply text-gray-400;
  }

  .dark .agrilift-border {
    @apply border-gray-700;
  }

  .dark .agrilift-input {
    @apply bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400;
  }

  .dark .agrilift-button-primary {
    @apply bg-green-700 hover:bg-green-600 text-white;
  }

  .dark .agrilift-button-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-gray-100;
  }

  /* Navigation specific dark theme */
  .dark .navbar-bg {
    @apply bg-gray-800 border-gray-700;
  }

  .dark .navbar-text {
    @apply text-gray-200;
  }

  .dark .navbar-text-hover {
    @apply hover:text-green-400;
  }

  .dark .navbar-active {
    @apply text-green-400 bg-gray-700;
  }

  /* Market and product cards dark theme */
  .dark .product-card {
    @apply bg-gray-800 border-gray-700 hover:bg-gray-750;
  }

  .dark .product-card-hover {
    @apply hover:shadow-xl hover:border-green-600;
  }

  /* Dashboard specific dark theme */
  .dark .dashboard-bg {
    @apply bg-gray-900;
  }

  .dark .dashboard-card {
    @apply bg-gray-800 border-gray-700;
  }

  .dark .dashboard-stat-card {
    @apply bg-gradient-to-r from-gray-800 to-gray-700 border-gray-600;
  }

  /* Form elements dark theme */
  .dark .form-input {
    @apply bg-gray-700 border-gray-600 text-gray-100 focus:border-green-500;
  }

  .dark .form-label {
    @apply text-gray-300;
  }

  .dark .form-select {
    @apply bg-gray-700 border-gray-600 text-gray-100;
  }

  /* Badge and status indicators dark theme */
  .dark .badge-success {
    @apply bg-green-800 text-green-200;
  }

  .dark .badge-warning {
    @apply bg-yellow-800 text-yellow-200;
  }

  .dark .badge-error {
    @apply bg-red-800 text-red-200;
  }

  .dark .badge-info {
    @apply bg-blue-800 text-blue-200;
  }
}
}

/* Navigation spacing utilities */
@layer utilities {
  .nav-spacing {
    @apply pt-20 lg:pt-24;
  }

  .nav-spacing-sm {
    @apply pt-16 lg:pt-20;
  }
}

/* Weather icon animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(251, 191, 36, 0.5), 0 0 10px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.8), 0 0 20px rgba(251, 191, 36, 0.5);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes bounce-gentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes pulse-soft {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes rotate-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fade-in-out {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 3s ease-in-out infinite;
}

.animate-rotate-slow {
  animation: rotate-slow 20s linear infinite;
}

.animate-fade-in-out {
  animation: fade-in-out 2s ease-in-out infinite;
}

/* Enhanced animations for machinery page */
@keyframes fade-in {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes slide-in-left {
  from { 
    opacity: 0; 
    transform: translateX(-30px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(0); 
  }
}

@keyframes slide-in-right {
  from { 
    opacity: 0; 
    transform: translateX(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(0); 
  }
}

@keyframes scale-in {
  from { 
    opacity: 0; 
    transform: scale(0.9); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

@keyframes hover-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.5s ease-out forwards;
}

.animate-hover-bounce:hover {
  animation: hover-bounce 0.6s ease-in-out;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Custom scrollbar for better UX */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Improved text clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
